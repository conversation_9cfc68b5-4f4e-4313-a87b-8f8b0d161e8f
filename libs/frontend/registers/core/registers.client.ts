import { RegisterEntry, RegisterType } from '.';
import {
  ManagementRegister,
  ManagementRegisterConfig,
  ReceivershipRegister,
  ReceivershipRegisterConfig,
  TransactionRegister,
  TransactionRegisterConfig
} from './registers';

export abstract class RegistersClient {
  abstract getManagementRegister(organizationId: number): Promise<ManagementRegister>;

  abstract getReceivershipRegister(organizationId: number): Promise<ReceivershipRegister>;

  abstract getTransactionRegister(organizationId: number): Promise<TransactionRegister>;

  abstract updateManagementRegister(args: UpdateManagementRegisterArgs): Promise<void>;

  abstract updateReceivershipRegister(args: UpdateReceivershipRegisterArgs): Promise<void>;

  abstract updateTransactionRegister(args: UpdateTransactionRegisterArgs): Promise<void>;

  abstract getNextRegisterNumber(args: GetNextNumberArgs): Promise<number | null>;

  abstract getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]>;

  abstract getRegisterEntriesLegacy(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]>;
}

export interface UpdateReceivershipRegisterArgs {
  config: ReceivershipRegisterConfig;
  id: number;
  organizationId: number;
}

export interface UpdateManagementRegisterArgs {
  config: ManagementRegisterConfig;
  id: number;
  organizationId: number;
}

export interface UpdateTransactionRegisterArgs {
  config: TransactionRegisterConfig;
  id: number;
  organizationId: number;
}

export interface GetNextNumberArgs {
  organizationId: number;
  registerType: string;
}

export interface GetRegisterEntriesArgs {
  filterNumber?: string;
  legalOperationId?: string;
  organizationId?: number;
  page?: number;
  pageSize?: number;
  registerType: RegisterType;
  search?: string;
  userId?: string;
}
