import './register-question.scss';
import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { MnInputText, MnSvg, MnTooltip } from '@mynotary/frontend/shared/ui';
import { selectOperation, selectContract } from '@mynotary/frontend/legals/api';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { RegisterAddEntryPopin } from '../add-register-entry-popin/add-register-entry-popin';
import { RegisterEntry } from '@mynotary/frontend/registers/core';
import { selectContractRegisterDefaultAnswer } from '@mynotary/frontend/registers/store';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { RegisterQuestionConfirmationPoppin } from './register-question-confirmation-poppin';
import { selectCurrentContractId, selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';

interface RegisterQuestionCommonProps extends MnProps {
  answer: Answer;
  className?: string;
  debounce: boolean;
  entry?: RegisterEntry;
  hasRegisterEntryManuallyCreatePermission: boolean;
  onChange?: (value: string | null) => void;
  onCloseConfirmationPoppin: () => void;
  onCloseFocus: () => void;
  onFocus: () => void;
  onResetEntry: () => void;
  openConfirmationPopin: boolean;
  question: RegisterFormQuestion;
}

enum RegisterEntryStatus {
  CLOSED = 'CLOSED',
  RESERVED = 'RESERVED',
  VALIDATED = 'VALIDATED'
}

export const RegisterQuestion = ({
  answer,
  className,
  debounce,
  entry,
  hasRegisterEntryManuallyCreatePermission,
  onChange,
  onCloseConfirmationPoppin,
  onCloseFocus,
  onFocus,
  onResetEntry,
  openConfirmationPopin,
  question
}: RegisterQuestionCommonProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const contractId = useSelector(selectCurrentContractId);
  const operationId = useSelector(selectCurrentOperationId);
  const operation = useSelector(selectOperation(operationId));
  const contract = useSelector(selectContract(contractId));
  const contractAnswer = useSelector(
    selectContractRegisterDefaultAnswer(question.register?.type, operation?.id, contract?.id)
  );
  const [isAdding, setIsAdding] = useState(false);

  const isRegisterNumberAvailable = !entry || entry.status === RegisterEntryStatus.CLOSED;

  const handleRegisterFinished = (entry?: RegisterEntry): void => {
    setIsAdding(false);
    // setEntry(entry); // TODO : vérifier que le onChange suffit à set l'entry au niveau supérieur
    onChange?.(entry?.answer['numero_registre'].value);
  };

  const handleError = async () => {
    dispatch(setErrorMessage('Une erreur est survenue lors de la prise de numéro. Vouz pouvez annuler et réessayer.'));
    setIsAdding(false);
  };

  const handleAddRegistry = (): void => {
    if (!isRegisterNumberAvailable) {
      return;
    }
    setIsAdding(true);
  };

  return (
    <div className={classNames('mn-register-question')}>
      <MnInputText
        className={className}
        debounceTime={debounce ? 500 : 0}
        defaultValue={question.default}
        disabled={!hasRegisterEntryManuallyCreatePermission || !isRegisterNumberAvailable}
        format={question.uppercase}
        onChange={onChange}
        onFocus={onFocus}
        placeholder={question.placeholder}
        required={!question.optional}
        value={answer?.value}
      />
      <MnTooltip content={'Vous ne pouvez pas reprendre un numéro sans clore le précédent'}>
        <div className='rd-add' onClick={handleAddRegistry}>
          <MnSvg
            className={classNames('rq-add-register-entry-tooltip', { disabled: !isRegisterNumberAvailable })}
            path='/assets/images/pictos/icon/info-light.svg'
            variant={isRegisterNumberAvailable ? 'primary' : 'gray500-primary'}
          />
          <div className={classNames('rq-add-register-entry', { disabled: !isRegisterNumberAvailable })}>
            Prendre un numéro
          </div>
        </div>
      </MnTooltip>
      {contractAnswer && (
        <RegisterAddEntryPopin
          contractId={contract.id}
          defaultAnswer={contractAnswer}
          mode={question.register.type}
          onClose={() => setIsAdding(false)}
          onError={handleError}
          onFinished={handleRegisterFinished}
          opened={isAdding}
        />
      )}
      <RegisterQuestionConfirmationPoppin
        answer={answer}
        onChange={onChange}
        onClose={onCloseConfirmationPoppin}
        onCloseFocus={onCloseFocus}
        onResetEntry={onResetEntry}
        openConfirmationPopin={openConfirmationPopin}
      />
    </div>
  );
};
