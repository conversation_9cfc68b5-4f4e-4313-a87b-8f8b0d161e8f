import React, { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { useFeatureState } from '@mynotary/frontend/features/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import {
  getRegisterEntries,
  selectCurrentManagementRegister,
  selectRegisterEntryByLegalOperationId
} from '@mynotary/frontend/registers/store';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestion } from './register-question';
import { RegisterQuestionWithoutAction } from './register-question-without-action';
import { some } from 'lodash';
import { selectContract } from '@mynotary/frontend/legals/api';
import { selectCurrentContractId, selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';
import { RegisterEntry } from '@mynotary/frontend/registers/core';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';

interface RegisterManagementQuestionProps extends MnProps {
  answer: Answer;
  className?: string;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterManagementQuestion = (props: RegisterManagementQuestionProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const role = useSelector(selectConnectedUserRole);
  const contractId = useSelector(selectCurrentContractId);
  const operationId = useSelector(selectCurrentOperationId);
  const contract = useSelector(selectContract(contractId));
  const organization = useSelector(selectCurrentOrganization);
  let disabled = props.disabled;
  const managementRegister = useSelector(selectCurrentManagementRegister);
  const entryOperation = useSelector(selectRegisterEntryByLegalOperationId(operationId, 'MANAGEMENT'));
  if (entryOperation) {
    disabled = true;
  }
  const [isOnFocusInput, setIsOnFocusInput] = useState(false);
  const [openConfirmationPopin, setOpenConfirmationPopin] = useState(false);
  const [entry, setEntry] = useState<RegisterEntry>();
  const { isActive: hasTransactionFeature } = useFeatureState(FeatureType.MANAGEMENT_REGISTER_ACCESS);
  const isAllowedContract = some(
    props.question.register.contracts,
    (contractType) => contractType === contract?.legalContractTemplateId
  );
  const isRegisterInitialized = managementRegister?.config != null;

  const hasRegisterEntryCreatePermission = hasPermission(
    PermissionType.CREATE_ORGANIZATION_REGISTER_ENTRY,
    role,
    EntityType.MANAGEMENT_REGISTER
  );

  const hasRegisterEntryManuallyCreatePermission =
    !isRegisterInitialized ||
    hasPermission(PermissionType.CREATE_ORGANIZATION_REGISTER_MANUALLY_ENTRY, role, EntityType.MANAGEMENT_REGISTER);

  const handleChange = async (value: string | null) => {
    if (value == null) {
      props.onChange?.(value);
      return;
    }
    const page = 0;
    const pageSize = 1;
    const register = await dispatch(
      getRegisterEntries('MANAGEMENT', `"numero_registre": {"value": ${value}}`, page, pageSize, organization?.id ?? 0)
    );
    setEntry(register?.entries?.[0]);

    props.onChange?.(value);
  };

  useEffect(() => {
    if (entry == null || !isOnFocusInput || entry?.operationId === operationId) {
      return;
    }
    setOpenConfirmationPopin(true);
  }, [entry, isOnFocusInput, operationId]);

  if (
    !disabled &&
    hasTransactionFeature &&
    hasRegisterEntryCreatePermission &&
    isAllowedContract &&
    isRegisterInitialized
  ) {
    return (
      <RegisterQuestion
        {...props}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
        onChange={handleChange}
        onCloseConfirmationPoppin={() => setOpenConfirmationPopin(false)}
        onCloseFocus={() => setIsOnFocusInput(false)}
        onFocus={() => setIsOnFocusInput(true)}
        onResetEntry={() => setEntry(undefined)}
        openConfirmationPopin={openConfirmationPopin}
      />
    );
  } else {
    return (
      <RegisterQuestionWithoutAction
        {...props}
        disabled={disabled}
        hasRegisterEntryCreatePermission={hasRegisterEntryCreatePermission}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
        isAllowedContract={isAllowedContract}
        isRegisterInitialized={isRegisterInitialized}
        onChange={handleChange}
        onCloseConfirmationPoppin={() => setOpenConfirmationPopin(false)}
        onCloseFocus={() => setIsOnFocusInput(false)}
        onFocus={() => setIsOnFocusInput(true)}
        onResetEntry={() => setEntry(undefined)}
        openConfirmationPopin={openConfirmationPopin}
      />
    );
  }
};
