import { RegisterEntriesController } from '@mynotary/backend/registers/feature';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { provideMembersTest } from '@mynotary/backend/members/test';
import { provideRegistersTest } from '../index';
import { RegisterEntryStatus, RegisterEntryType } from '@mynotary/backend/registers/core';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { PlanType } from '@mynotary/crossplatform/billings/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

describe(RegisterEntriesController.name, () => {
  it('should retrieve user entries only', async () => {
    const { client, uniqueInfos } = await setup();

    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, type: 'MANAGEMENT', userId: uniqueInfos.user.userId })
      .send();

    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0]).toEqual({
      answer: {},
      contractId: uniqueInfos.contract.id.toString(),
      creationTime: expect.any(String),
      creatorEmail: '<EMAIL>',
      creatorFirstname: 'Foo',
      creatorId: uniqueInfos.user.userId,
      creatorLastname: 'BAR',
      creatorOrganizationId: uniqueInfos.user.organizationId,
      id: expect.any(String),
      legalOperationId: uniqueInfos.operation.id,
      operationId: parseInt(uniqueInfos.operation.id),
      operationLabel: null,
      status: 'VALIDATED',
      type: 'MANAGEMENT'
    });
    expect(response.body.items[0].creatorId).toEqual(uniqueInfos.user.userId);
  });

  it('should retrieve organization entries only', async () => {
    const { baseInfos, client } = await setup();
    const response = await client
      .get(`/register-entries`)
      .query({
        organizationId: baseInfos.user.organizationId,
        page: 1,
        pageSize: 10,
        type: 'TRANSACTION',
        userId: baseInfos.user.userId
      })
      .send();

    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(2);
    expect(
      response.body.items.every(
        (item: { creatorOrganizationId: string }) => item.creatorOrganizationId === baseInfos.user.organizationId
      )
    );
  });

  it('should retrieve legalOperationId entry only', async () => {
    const { client, uniqueInfos } = await setup();
    const response = await client
      .get(`/register-entries`)
      .query({ legalOperationId: uniqueInfos.operation.id, page: 1, pageSize: 10, type: 'MANAGEMENT' })
      .send();

    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].legalOperationId).toEqual(uniqueInfos.operation.id);
  });

  it('should throw if entryNumber and search are provided', async () => {
    const { client } = await setup();
    const response = await client
      .get(`/register-entries`)
      .query({ entryNumber: 97, page: 1, pageSize: 10, search: 'Observation', type: 'TRANSACTION' })
      .send();

    expect(response.statusCode).toEqual(422);
  });

  fit('should retrieve entries by entryNumber', async () => {
    const { client, registerForTest, testingRepos } = await setup();
    await testingRepos.register.updateRegisterEntry({
      entryNumber: 97,
      id: registerForTest.id.toString(),
      status: RegisterEntryStatus.VALIDATED
    });
    const response = await client
      .get(`/register-entries`)
      .query({ entryNumber: 97, page: 1, pageSize: 10, type: 'TRANSACTION' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].answer.numero_registre.value).toEqual(97);
  });

  it('should retrieve entries by search (in entryNumber)', async () => {
    const { client, registerForTest, testingRepos } = await setup();
    await testingRepos.register.updateRegisterEntry({
      entryNumber: 980,
      id: registerForTest.id.toString(),
      status: RegisterEntryStatus.VALIDATED
    });
    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, search: '98', type: 'TRANSACTION' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].answer.numero_registre.value).toEqual(980);
  });

  it('should retrieve entries by search (in creator infos)', async () => {
    const { baseInfos, client, testingRepos } = await setup();

    await testingRepos.users.updateUser({
      firstname: 'Jean',
      id: baseInfos.user.userId,
      lastname: 'Dupont'
    });

    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, search: 'Dupont', type: 'TRANSACTION' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(2);
    expect(response.body.items.every((item: { creatorLastname: string }) => item.creatorLastname === 'Dupont'));
  });

  it('should retrieve entries by type', async () => {
    const { client, featureOtherOrg, testingRepos, uniqueInfos } = await setup();
    await testingRepos.register.createRegisterEntry({
      contractId: uniqueInfos.contract.id,
      featureId: featureOtherOrg.id,
      organizationId: uniqueInfos.user.organizationId,
      status: RegisterEntryStatus.VALIDATED,
      type: RegisterEntryType.RECEIVERSHIP,
      userId: uniqueInfos.user.organizationId
    });

    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, type: 'RECEIVERSHIP' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].type).toEqual('RECEIVERSHIP');
  });

  it('should retrieve entries by multiple filters', async () => {
    const { client, testingRepos, uniqueInfos } = await setup();
    await testingRepos.users.updateUser({
      firstname: 'John',
      id: uniqueInfos.user.userId,
      lastname: 'Doe'
    });

    const response = await client
      .get(`/register-entries`)
      .query({
        legalOperationId: uniqueInfos.operation.id,
        page: 1,
        pageSize: 10,
        search: 'Doe',
        type: 'MANAGEMENT',
        userId: uniqueInfos.user.userId
      })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].type).toEqual('MANAGEMENT');
    expect(response.body.items[0].creatorLastname).toEqual('Doe');
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: RegisterEntriesController,
      providers: [...provideMembersTest(), ...provideRegistersTest()]
    });

    const testingRepos = getService(TestingRepositories);

    /**
     * This setup simulate 2 organizations with multiple users, operations, contracts and register entries.
     * Organization A : 3 users, 3 operations, 3 contracts, 3 TRANSACTION register entries
     * Organization B : 1 user, 1 operation, 1 contract, 1 TRANSACTION register entry, 1 MANAGEMENT register entry
     */
    const createContext = async () => {
      const baseMember = await testingRepos.createMember({
        permissions: [
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          }
        ],
        planType: PlanType.PREMIUM
      });
      const uniqueMember = await testingRepos.createMember({
        permissions: [
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.RECEIVERSHIP_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.RECEIVERSHIP_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          }
        ],
        planType: PlanType.PREMIUM
      });
      const memberSameOrg = await testingRepos.addMember({
        organizationId: baseMember.organizationId,
        permissions: [
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          }
        ]
      });

      const baseOperation = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: baseMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: baseMember.userId
      });
      const operationSameUser = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: baseMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: baseMember.userId
      });
      const operationSameOrg = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: baseMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: memberSameOrg.userId
      });
      const operationUnique = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: uniqueMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: uniqueMember.userId
      });

      const firstContract = await testingRepos.contracts.createMandat({
        operationId: baseOperation.id,
        userId: baseMember.userId
      });
      const secondContract = await testingRepos.contracts.createMandat({
        operationId: operationSameUser.id,
        userId: baseMember.userId
      });
      await testingRepos.contracts.createMandat({
        operationId: operationSameOrg.id,
        userId: memberSameOrg.userId
      });
      const contractUnique = await testingRepos.contracts.createMandat({
        operationId: operationUnique.id,
        userId: uniqueMember.userId
      });

      const featureBaseOrg = await testingRepos.features.createFeature({
        organizationId: baseMember.organizationId,
        type: FeatureType.TRANSACTION_REGISTER_ACCESS
      });
      await testingRepos.features.createFeature({
        organizationId: baseMember.organizationId,
        type: FeatureType.MANAGEMENT_REGISTER_ACCESS
      });
      await testingRepos.features.createFeature({
        organizationId: baseMember.organizationId,
        type: FeatureType.RECEIVERSHIP_REGISTER_ACCESS
      });

      const featureOtherOrg = await testingRepos.features.createFeature({
        organizationId: uniqueMember.organizationId,
        type: FeatureType.TRANSACTION_REGISTER_ACCESS
      });
      await testingRepos.features.createFeature({
        organizationId: uniqueMember.organizationId,
        type: FeatureType.MANAGEMENT_REGISTER_ACCESS
      });
      await testingRepos.features.createFeature({
        organizationId: uniqueMember.organizationId,
        type: FeatureType.RECEIVERSHIP_REGISTER_ACCESS
      });

      const registerForTest = await testingRepos.register.createRegisterEntry({
        contractId: firstContract.id,
        featureId: featureBaseOrg.id,
        organizationId: baseMember.organizationId,
        status: RegisterEntryStatus.VALIDATED,
        type: RegisterEntryType.TRANSACTION,
        userId: baseMember.userId
      });
      await testingRepos.register.createRegisterEntry({
        contractId: firstContract.id,
        featureId: featureBaseOrg.id,
        organizationId: baseMember.organizationId,
        status: RegisterEntryStatus.VALIDATED,
        type: RegisterEntryType.TRANSACTION,
        userId: baseMember.userId
      });
      await testingRepos.register.createRegisterEntry({
        contractId: secondContract.id,
        featureId: featureBaseOrg.id,
        organizationId: baseMember.organizationId,
        status: RegisterEntryStatus.VALIDATED,
        type: RegisterEntryType.TRANSACTION,
        userId: memberSameOrg.userId
      });
      await testingRepos.register.createRegisterEntry({
        contractId: contractUnique.id,
        featureId: featureOtherOrg.id,
        organizationId: uniqueMember.organizationId,
        status: RegisterEntryStatus.VALIDATED,
        type: RegisterEntryType.TRANSACTION,
        userId: memberSameOrg.userId
      });
      await testingRepos.register.createRegisterEntry({
        contractId: contractUnique.id,
        featureId: featureOtherOrg.id,
        organizationId: uniqueMember.organizationId,
        status: RegisterEntryStatus.VALIDATED,
        type: RegisterEntryType.MANAGEMENT,
        userId: uniqueMember.userId
      });

      return {
        baseInfos: { operation: baseOperation, user: baseMember },
        featureOtherOrg,
        registerForTest,
        uniqueInfos: { contract: contractUnique, operation: operationUnique, user: uniqueMember }
      };
    };
    const { baseInfos, featureOtherOrg, registerForTest, uniqueInfos } = await createContext();
    return {
      baseInfos,
      client,
      featureOtherOrg,
      registerForTest,
      testingRepos,
      uniqueInfos
    };
  }
});
