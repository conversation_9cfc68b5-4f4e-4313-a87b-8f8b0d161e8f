import { PrismaService, Prisma } from '@mynotary/backend/shared/prisma-infra';
import {
  GetRegisterEntriesArgs,
  RegisterEntriesRepository,
  RegisterEntry,
  RegisterEntryStatus,
  RegisterEntryType,
  RegisterEntryUpdate
} from '@mynotary/backend/registers/core';
import { convertEnum, JSONValue, NotFoundError } from '@mynotary/crossplatform/shared/util';
import { Injectable } from '@nestjs/common';
import { AnswerDict } from '@mynotary/crossplatform/records/api';

@Injectable()
export class RegisterEntriesRepositoryImpl implements RegisterEntriesRepository {
  constructor(private prisma: PrismaService) {}

  async getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]> {
    args.page = Math.max(args.page ?? 0, 1);
    args.pageSize = Math.max(args.pageSize ?? 0, 20);
    const skip = (args.page - 1) * args.pageSize;
    const take = args.pageSize ?? 10;
    const whereConditions: Prisma.register_entryWhereInput = {};
    if (args.organizationId) {
      whereConditions.organization_id = parseInt(args.organizationId);
    }

    if (args.legalOperationId) {
      whereConditions.operation_contract = {
        operation_id: parseInt(args.legalOperationId)
      };
    }

    if (args.userId) {
      whereConditions.creator_user_id = parseInt(args.userId);
    }

    if (args.type) {
      whereConditions.type = args.type;
    }

    if (args.entryNumber) {
      whereConditions.answer = {
        equals: args.entryNumber,
        path: ['numero_registre', 'value']
      };
    }

    if (args.registerId) {
      whereConditions.feature_id = parseInt(args.registerId);
    }

    if (args.search) {
      const searchConditions: any[] = [
        {
          user: {
            OR: [
              { firstname: { contains: args.search, mode: 'insensitive' } },
              { lastname: { contains: args.search, mode: 'insensitive' } }
            ]
          }
        }
      ];

      // Check if search is a number for entryNumber search
      const searchAsNumber = parseInt(args.search, 10);
      if (!isNaN(searchAsNumber) && searchAsNumber.toString() === args.search.trim()) {
        // Exact number match for entryNumber
        searchConditions.push({
          answer: {
            path: ['numero_registre', 'value'],
            equals: searchAsNumber
          }
        });
      }

      whereConditions.OR = searchConditions;
    }

    console.log('SHERLOCK WHERE CONDITIONS', whereConditions, JSON.stringify(whereConditions.OR));

    const entries = await this.prisma.register_entry.findMany({
      orderBy: {
        creation_time: 'desc'
      },
      select: SELECT_REGISTER_COLUMN_WITH_USER_AND_OPERATION,
      skip,
      take,
      where: whereConditions
    });
    return entries.map((entry) => {
      return {
        answer: entry.answer as AnswerDict,
        contractId: entry?.contract_id?.toString(),
        creationTime: entry.creation_time.toISOString(),
        creatorEmail: entry.user?.email,
        creatorFirstname: entry.user?.firstname,
        creatorId: entry.user.id.toString(),
        creatorLastname: entry.user?.lastname,
        creatorOrganizationId: entry.organization_id.toString(),
        id: entry.id.toString(),
        observations: (entry.answer as AnswerDict)?.observations_registre?.value,
        operationId: entry.operation_contract?.operation_id,
        operationLabel: entry.operation_contract?.legal_component_operation.label,
        status: convertEnum(RegisterEntryStatus, entry.status),
        type: convertEnum(RegisterEntryType, entry.type)
      };
    });
  }

  async updateRegisterEntry(args: RegisterEntryUpdate): Promise<void> {
    const entry = await this.prisma.register_entry.findUnique({
      select: SELECT_REGISTER_COLUMN,
      where: { id: parseInt(args.id) }
    });

    if (entry == null) {
      throw new NotFoundError({ id: args.id, resource: 'RegisterEntry' });
    }

    const answer = entry.answer as JSONValue;

    await this.prisma.register_entry.update({
      data: {
        answer: {
          ...answer,
          observations_registre: {
            value: args.observations != null ? args.observations : answer?.['observations_registre']?.value
          }
        },
        status: args.status != null ? args.status : entry.status
      },
      where: { id: parseInt(args.id) }
    });
  }
}

const SELECT_REGISTER_COLUMN = {
  answer: true,
  contract_id: true,
  creation_time: true,
  creator_user_id: true,
  feature_id: true,
  id: true,
  status: true,
  type: true
};

const SELECT_REGISTER_COLUMN_WITH_USER_AND_OPERATION = Prisma.validator<Prisma.register_entrySelect>()({
  answer: true,
  contract_id: true,
  creation_time: true,
  creator_user_id: true,
  feature_id: true,
  id: true,
  operation_contract: {
    select: {
      id: true,
      legal_component_operation: {
        select: {
          label: true
        }
      },
      operation_id: true
    }
  },
  organization_id: true,
  status: true,
  type: true,
  user: {
    select: {
      email: true,
      firstname: true,
      id: true,
      lastname: true
    }
  }
});
