import { Injectable, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from './generated-client';
import { getGcpProject, getProject, isProductionProject } from '@mynotary/backend/shared/util';
import { isEmpty } from 'lodash';

// eslint-disable-next-line @nx/enforce-module-boundaries
import { EnvService } from '@mynotary/backend/secrets/core';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleDestroy, OnModuleInit {
  constructor(envService: EnvService) {
    const datasourceUrl = getDatabaseUrl(envService);
    super({
      datasourceUrl,
      log: [
        'query',
        {
          emit: 'event',
          level: 'query'
        }
      ]
    });
  }

  async onModuleInit() {
    process.on('beforeExit', async () => {
      console.error('Prism<PERSON> is exiting prematurely');
    });
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}

/**
 * Databse url is composed of the following parts: user, password, database name and an optional host
 */
function getDatabaseUrl(envService: EnvService) {
  const user = getDatabaseUser(envService);
  const password = getDatabasePassword(envService);
  const host = getDatabaseHost(envService);
  const databaseName = getDatabaseName(envService);
  const gcpHost = getGcpHost(envService);
  const connectionLimit = getConnectionLimit(gcpHost);

  return `postgresql://${user}:${password}@${host}/${databaseName}${gcpHost}${connectionLimit}`;
}

/**
 * Database user is the same as the application name (eg: api-mynotary, api-signatures...) except for local
 * environment we always use "api"
 */
function getDatabaseUser(envService: EnvService) {
  if (isProductionProject() && !envService.isTestEnv()) {
    return envService.database.user;
  }
  return 'api';
}

/**
 * Database password are stored in the secrets manager, except for local environment we always use "api"
 */
function getDatabasePassword(envService: EnvService) {
  if (isProductionProject() && !envService.isTestEnv()) {
    return envService.database.password;
  }
  return 'api';
}

/**
 * We should always use localhost, the only case when we use a defined host is when we are debugging locally with the
 * preprouction or production database.
 */
function getDatabaseHost(envService: EnvService) {
  return envService.database.host ?? 'localhost';
}

/**
 * Database name is always 'mynotary' except for test environment we use unique name with random uuid
 */
function getDatabaseName(envService: EnvService) {
  if (envService.isTestEnv()) {
    return process.env.TESTING_DB_NAME;
  }
  return 'mynotary';
}

/**
 * Host is necessary for production project running on cloud run services.
 */
function getGcpHost(envService: EnvService) {
  if (envService.debug || envService.isTestEnv()) {
    return '';
  }

  const gcpProject = getGcpProject();

  if (gcpProject === 'mynotary-production') {
    return `?host=/cloudsql/${gcpProject}:europe-west9:database-production`;
  } else if (gcpProject === 'mynotary-preproduction') {
    return `?host=/cloudsql/${gcpProject}:europe-west9:database-preproduction`;
  }
  return '';
}

/**
 * Calculation Explanation:
 *
 * Based on the provided specifications:
 * - Maximum connections for the database:
 *   - Production: 800 connections
 *   - Preproduction: 100 connections
 * - Total number of servers: 5
 * - Maximum instances per server: 2 (total instances = 5 × 2 = 10)
 * - Scheduled jobs: Between 1 and 10
 *
 * Steps to determine the number of connections per server:
 * 1. Reserve a fixed percentage of total connections for scheduled jobs (default: 10%).
 *    Additionally, reserve an extra 10% as a safety margin.
 *    For example, in production:
 *      - 10% for jobs: 80 connections
 *      - 10% safety margin: 80 connections
 *      Total reserved: 80 + 80 = 160 connections.
 * 2. Subtract the reserved connections (for jobs + safety margin) from the total database connections.
 *    Available connections after reservation:
 *    - Production: 800 - 160 = 640
 *    - Preproduction: 100 - 20 = 80
 * 3. Distribute the remaining connections equally across all instances
 *    (servers × instances per server).
 *    - Connections per instance:
 *      - Production: 640 ÷ 10 = 64
 *      - Preproduction: 80 ÷ 10 = 8
 *
 * This ensures:
 * - 10% of the total connections are reserved for scheduled jobs.
 * - 10% of the total connections are reserved as a safety margin.
 * - Each server instance receives an equal share of the remaining connections.
 */

function getConnectionLimit(gcpHost: string) {
  const gcpProject = getProject();
  const limit = gcpProject === 'production' ? '64' : '8';

  return isEmpty(gcpHost) ? `?connection_limit=${limit}` : `&connection_limit=${limit}`;
}
