import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { RegisterEntryStatus, RegisterEntryType } from '@mynotary/backend/registers/api';
import { assertNotNull, JSONValue, NotFoundError } from '@mynotary/crossplatform/shared/util';

export class RegisterTestingRepository {
  constructor(private prisma: PrismaService) {}

  async createRegisterEntry(args: CreateRegisterEntryArgs) {
    const entry = await this.prisma.register_entry.create({
      data: {
        answer: {},
        contract_id: parseInt(args.contractId),
        creator_user_id: parseInt(args.userId),
        feature_id: parseInt(args.featureId),
        organization_id: parseInt(args.organizationId),
        status: args.status,
        type: args.type
      }
    });

    return {
      id: entry.id
    };
  }

  async getRegisterEntry(id: string) {
    const entry = await this.prisma.register_entry.findUnique({
      where: { id: parseInt(id) }
    });
    assertNotNull(entry, 'regsiter entry');

    return { status: entry.status };
  }

  async updateRegisterEntry(args: RegisterEntryUpdate) {
    const entry = await this.prisma.register_entry.findUnique({
      select: {
        answer: true,
        contract_id: true,
        creation_time: true,
        creator_user_id: true,
        feature_id: true,
        id: true,
        status: true,
        type: true
      },
      where: { id: parseInt(args.id) }
    });

    if (entry == null) {
      throw new NotFoundError({ id: args.id, resource: 'RegisterEntry' });
    }
    const answer = entry.answer as JSONValue;
    await this.prisma.register_entry.update({
      data: {
        answer: {
          ...answer,
          numero_registre: {
            value: args.entryNumber != null ? args.entryNumber : answer?.['numero_registre']?.value
          },
          observations_registre: {
            value: args.observations != null ? args.observations : answer?.['observations_registre']?.value
          }
        }
      },
      where: { id: parseInt(args.id) }
    });
  }
}

interface CreateRegisterEntryArgs {
  contractId: string;
  featureId: string;
  organizationId: string;
  status: RegisterEntryStatus;
  type: RegisterEntryType;
  userId: string;
}

export interface RegisterEntryUpdate {
  entryNumber?: number;
  id: string;
  observations?: string;
  status?: RegisterEntryStatus;
}
